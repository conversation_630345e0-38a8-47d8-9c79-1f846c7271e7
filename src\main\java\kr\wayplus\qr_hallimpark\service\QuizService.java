package kr.wayplus.qr_hallimpark.service;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.common.service.impl.BaseAdminListService;
import kr.wayplus.qr_hallimpark.mapper.QuizMapper;
import kr.wayplus.qr_hallimpark.mapper.QuizContentMapper;
import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.Quiz;
import kr.wayplus.qr_hallimpark.model.ContentCategory;
import kr.wayplus.qr_hallimpark.model.QuizContent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 문제 서비스
 * - 문제 CRUD 비즈니스 로직 처리
 * - 공통 리스트 기능 지원
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class QuizService extends BaseAdminListService<Quiz> {
    private final QuizMapper quizMapper;
    private final QuizContentMapper quizContentMapper;
    private final ContentCategoryService contentCategoryService;

    // ========== 공통 리스트 기능 구현 ==========

    @Override
    protected AdminListMapper<Quiz> getMapper() {
        return quizMapper;
    }

    @Override
    protected String getTableName() {
        return "quiz_master";
    }

    @Override
    protected String getDefaultSortField() {
        return "quiz_id"; // 최신 생성 순으로 정렬 (ID가 높을수록 최신)
    }

    @Override
    public AdminListSearch createDefaultSearchCondition() {
        return AdminListSearch.builder()
                .page(1)
                .size(20)
                .sortField("quiz_id")
                .sortDirection("DESC") // 내림차순으로 최신 항목이 위로
                .includeDeleted(false) // 삭제된 항목 제외
                .tableName(getTableName())
                .build();
    }

    @Override
    protected void validateDomainSpecificConditions(AdminListSearch searchCondition) {
        // 허용된 검색 필드 검증
        List<String> allowedSearchFields = Arrays.asList("question", "category_name", "quiz_type");
        validateSearchFields(searchCondition, allowedSearchFields);

        // 허용된 정렬 필드 검증
        List<String> allowedSortFields = Arrays.asList("quiz_id", "question", "category_name", "quiz_type", "create_date", "last_update_date");
        validateSortField(searchCondition, allowedSortFields);
    }

    // ========== 문제 관련 비즈니스 로직 ==========

    /**
     * 활성화된 문제 목록 조회 (API용)
     * @param categoryId 카테고리 ID (선택사항)
     * @param page 페이지 번호
     * @param size 페이지 크기
     * @return 페이징된 활성화 문제 목록
     */
    public AdminListResponse<Quiz> findActiveQuizzes(Long categoryId, int page, int size) {
        log.debug("Finding active quizzes - categoryId: {}, page: {}, size: {}", categoryId, page, size);

        AdminListSearch searchCondition = AdminListSearch.builder()
                .page(page)
                .size(Math.min(size, 100)) // 최대 100개로 제한
                .categoryId(categoryId)
                .includeDeleted(false)
                .status("ACTIVE") // 활성화된 문제만
                .sortField("quiz_id")
                .sortDirection("DESC")
                .tableName("quiz_master")
                .build();

        return findListWithConditions(searchCondition);
    }

    /**
     * 카테고리별 문제 목록 조회 (관리자용)
     * @param categoryId 카테고리 ID
     * @param page 페이지 번호
     * @param size 페이지 크기
     * @return 페이징된 문제 목록
     */
    public AdminListResponse<Quiz> findQuizzesByCategory(Long categoryId, int page, int size) {
        log.debug("Finding quizzes by category - categoryId: {}, page: {}, size: {}", categoryId, page, size);

        AdminListSearch searchCondition = AdminListSearch.builder()
                .page(page)
                .size(size)
                .categoryId(categoryId)
                .includeDeleted(false)
                .sortField("quiz_id")
                .sortDirection("DESC")
                .tableName("quiz_master")
                .build();

        return findListWithConditions(searchCondition);
    }

    /**
     * 사용자별 문제 목록 조회
     * @param userId 사용자 ID
     * @param page 페이지 번호
     * @param size 페이지 크기
     * @return 페이징된 문제 목록
     */
    public AdminListResponse<Quiz> findQuizzesByUser(String userId, int page, int size) {
        log.debug("Finding quizzes by user - userId: {}, page: {}, size: {}", userId, page, size);

        AdminListSearch searchCondition = AdminListSearch.builder()
                .page(page)
                .size(size)
                .userId(userId)
                .includeDeleted(false)
                .sortField("quiz_id")
                .sortDirection("DESC")
                .tableName("quiz_master")
                .build();

        return findListWithConditions(searchCondition);
    }
    
    /**
     * 모든 문제 목록 조회
     * @return 문제 목록
     */
    public List<Quiz> findAllQuizzes() {
        log.debug("Finding all quizzes");
        return quizMapper.selectQuizList();
    }
    
    /**
     * 문제 ID로 문제 조회
     * @param quizId 문제 ID
     * @return 문제 정보
     */
    public Quiz findQuizById(Long quizId) {
        log.debug("Finding quiz by ID: {}", quizId);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        Quiz quiz = quizMapper.selectQuizById(quizId);
        if (quiz == null) {
            throw new IllegalArgumentException("존재하지 않는 문제입니다. ID: " + quizId);
        }

        return quiz;
    }

    /**
     * 문제 ID로 문제 조회 (다국어 콘텐츠 포함)
     * @param quizId 문제 ID
     * @return 문제 정보 (다국어 콘텐츠 포함)
     */
    public Quiz findQuizByIdWithContents(Long quizId) {
        log.debug("Finding quiz with contents by ID: {}", quizId);

        // 기본 문제 정보 조회
        Quiz quiz = findQuizById(quizId);

        // 다국어 콘텐츠 조회
        List<QuizContent> contents = quizContentMapper.selectQuizContentsByQuizId(quizId);
        quiz.setLanguages(contents);

        return quiz;
    }

    /**
     * 특정 카테고리의 문제 목록 조회
     * @param categoryId 카테고리 ID
     * @return 문제 목록
     */
    public List<Quiz> findQuizzesByCategoryId(Long categoryId) {
        log.debug("Finding quizzes by category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        return quizMapper.selectQuizListByCategoryId(categoryId);
    }

    /**
     * 특정 카테고리와 그 하위 카테고리의 문제 목록 조회
     * @param categoryId 상위 카테고리 ID
     * @return 문제 목록
     */
    public List<Quiz> findQuizzesWithChildCategories(Long categoryId) {
        log.debug("Finding quizzes with child categories for category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        return quizMapper.selectQuizListByCategoryIdWithChildren(categoryId);
    }

    /**
     * 최상위 카테고리 목록 조회 (문제 관리용)
     * @return 최상위 카테고리 목록
     */
    public List<ContentCategory> findRootCategoriesForQuizManagement() {
        log.debug("Finding root categories for quiz management");
        return contentCategoryService.findRootCategories();
    }

    /**
     * 하위 카테고리 목록 조회
     * @param parentId 부모 카테고리 ID
     * @return 하위 카테고리 목록
     */
    public List<ContentCategory> findChildCategories(Long parentId) {
        log.debug("Finding child categories for parent ID: {}", parentId);
        return contentCategoryService.findChildCategories(parentId);
    }

    /**
     * 카테고리별 문제 개수 조회
     * @param categoryId 카테고리 ID
     * @return 문제 개수
     */
    public int countQuizzesByCategoryId(Long categoryId) {
        log.debug("Counting quizzes by category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        return quizMapper.countQuizByCategoryId(categoryId);
    }

    // ========== 문제 CRUD 메서드 (향후 구현용) ==========
    
    /**
     * 문제 등록
     * @param quiz 문제 정보
     * @return 등록된 문제 정보
     */
    @Transactional
    public Quiz createQuiz(Quiz quiz) {
        log.debug("Creating quiz: {}", quiz);

        validateQuiz(quiz);

        // 지원 언어 검증
        if (quiz.getSupportedLanguages() == null || quiz.getSupportedLanguages().isEmpty()) {
            throw new IllegalArgumentException("최소 하나 이상의 언어를 선택해야 합니다.");
        }

        // 한국어는 필수
        if (!quiz.getSupportedLanguages().contains("ko")) {
            throw new IllegalArgumentException("한국어는 필수 언어입니다.");
        }

        // 1. quiz_master 테이블에 문제 등록
        int result = quizMapper.insertQuiz(quiz);
        if (result != 1) {
            throw new RuntimeException("문제 등록에 실패했습니다.");
        }

        // 2. quiz_content 테이블에 지원 언어별 기본 레코드 생성
        if (quiz.getSupportedLanguages() != null && !quiz.getSupportedLanguages().isEmpty()) {
            for (String langCode : quiz.getSupportedLanguages()) {
                QuizContent content = new QuizContent();
                content.setQuizId(quiz.getQuizId());
                content.setLangCode(langCode);
                content.setQuestion(""); // 빈 문제 내용으로 초기화 (추후 별도 페이지에서 입력)
                content.setOptions(null);
                content.setCreateId(quiz.getCreateId());
                content.setDeleteYn("N");

                int contentResult = quizContentMapper.insertQuizContent(content);
                if (contentResult != 1) {
                    throw new RuntimeException("다국어 콘텐츠 등록에 실패했습니다. 언어: " + langCode);
                }
            }
        }

        log.info("Quiz created successfully. ID: {}", quiz.getQuizId());
        return quiz;
    }

    /**
     * 문제 수정
     * @param quiz 문제 정보
     * @return 수정된 문제 정보
     */
    @Transactional
    public Quiz updateQuiz(Quiz quiz) {
        log.debug("Updating quiz: {}", quiz);

        validateQuiz(quiz);

        // 기존 문제 존재 여부 확인
        findQuizById(quiz.getQuizId());

        // 지원 언어 검증
        if (quiz.getSupportedLanguages() == null || quiz.getSupportedLanguages().isEmpty()) {
            throw new IllegalArgumentException("최소 하나 이상의 언어를 선택해야 합니다.");
        }

        // 한국어는 필수
        if (!quiz.getSupportedLanguages().contains("ko")) {
            throw new IllegalArgumentException("한국어는 필수 언어입니다.");
        }

        // 1. quiz_master 테이블 수정
        int result = quizMapper.updateQuiz(quiz);
        if (result != 1) {
            throw new RuntimeException("문제 수정에 실패했습니다.");
        }

        // 2. 지원 언어가 변경된 경우 quiz_content 업데이트
        if (quiz.getSupportedLanguages() != null && !quiz.getSupportedLanguages().isEmpty()) {
            // 새로 추가된 언어에 대해 빈 콘텐츠 생성
            for (String langCode : quiz.getSupportedLanguages()) {
                // 기존 콘텐츠 존재 여부 확인
                int existingCount = quizContentMapper.countQuizContentByQuizIdAndLangCode(
                    quiz.getQuizId(), langCode);

                if (existingCount == 0) {
                    // 새 언어 콘텐츠 생성
                    QuizContent content = new QuizContent();
                    content.setQuizId(quiz.getQuizId());
                    content.setLangCode(langCode);
                    content.setQuestion(""); // 빈 문제 내용으로 초기화
                    content.setOptions(null);
                    content.setCreateId(quiz.getLastUpdateId());
                    content.setDeleteYn("N");

                    int contentResult = quizContentMapper.insertQuizContent(content);
                    if (contentResult != 1) {
                        throw new RuntimeException("다국어 콘텐츠 추가에 실패했습니다. 언어: " + langCode);
                    }
                }
            }
        }

        log.info("Quiz updated successfully. ID: {}", quiz.getQuizId());
        return quiz;
    }

    /**
     * 문제 상태만 업데이트
     * @param quizId 문제 ID
     * @param status 새로운 상태
     * @param lastUpdateId 수정자 ID
     * @return 수정된 문제 정보
     */
    @Transactional
    public Quiz updateQuizStatus(Long quizId, String status, String lastUpdateId) {
        log.debug("Updating quiz status. ID: {}, Status: {}", quizId, status);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("상태는 필수입니다.");
        }

        if (lastUpdateId == null || lastUpdateId.trim().isEmpty()) {
            throw new IllegalArgumentException("수정자 ID는 필수입니다.");
        }

        // 기존 문제 존재 여부 확인
        Quiz existingQuiz = findQuizById(quizId);

        // quiz_master 테이블 상태만 수정
        int result = quizMapper.updateQuizStatus(quizId, status, lastUpdateId);
        if (result != 1) {
            throw new RuntimeException("문제 상태 수정에 실패했습니다.");
        }

        // 업데이트된 정보로 Quiz 객체 반환
        existingQuiz.setStatus(status);
        existingQuiz.setLastUpdateId(lastUpdateId);

        log.info("Quiz status updated successfully. ID: {}, Status: {}", quizId, status);
        return existingQuiz;
    }

    /**
     * 문제 다국어 지원 설정 변경
     * @param quizId 문제 ID
     * @param langCode 언어 코드
     * @param active 활성화 여부
     * @param lastUpdateId 수정자 ID
     */
    @Transactional
    public void toggleQuizLanguageSupport(Long quizId, String langCode, Boolean active, String lastUpdateId) {
        log.debug("Toggling quiz language support. Quiz ID: {}, Language: {}, Active: {}", quizId, langCode, active);

        // 문제 존재 여부 확인
        findQuizById(quizId);

        // 한국어는 비활성화할 수 없음
        if ("ko".equals(langCode) && !active) {
            throw new IllegalArgumentException("한국어는 기본 언어로 비활성화할 수 없습니다.");
        }

        // 기존 콘텐츠 존재 여부 확인
        int existingCount = quizContentMapper.countQuizContentByQuizIdAndLangCode(quizId, langCode);

        if (active) {
            // 활성화: 기존 콘텐츠 확인 (삭제된 것도 포함)
            QuizContent existingContent = quizContentMapper.selectQuizContentByQuizIdAndLangCodeIncludeDeleted(quizId, langCode);

            if (existingContent == null) {
                // 콘텐츠가 전혀 없으면 새로 생성
                QuizContent content = new QuizContent();
                content.setQuizId(quizId);
                content.setLangCode(langCode);
                content.setQuestion(""); // 빈 문제 내용으로 초기화
                content.setOptions(null);
                content.setCreateId(lastUpdateId);
                content.setDeleteYn("N");

                int result = quizContentMapper.insertQuizContent(content);
                if (result != 1) {
                    throw new RuntimeException("다국어 콘텐츠 생성에 실패했습니다. 언어: " + langCode);
                }
            } else if ("Y".equals(existingContent.getDeleteYn())) {
                // 삭제된 콘텐츠가 있으면 활성화
                int result = quizContentMapper.activateQuizContentByQuizIdAndLangCode(quizId, langCode, lastUpdateId);
                if (result == 0) {
                    throw new RuntimeException("다국어 콘텐츠 활성화에 실패했습니다. 언어: " + langCode);
                }
            }
            // 이미 활성화된 콘텐츠가 있으면 아무것도 하지 않음
        } else {
            // 비활성화: 논리 삭제
            if (existingCount > 0) {
                int result = quizContentMapper.deleteQuizContentByQuizIdAndLangCode(quizId, langCode, lastUpdateId);
                if (result == 0) {
                    throw new RuntimeException("다국어 콘텐츠 비활성화에 실패했습니다. 언어: " + langCode);
                }
            }
        }

        log.info("Quiz language support toggled successfully. Quiz ID: {}, Language: {}, Active: {}", quizId, langCode, active);
    }

    /**
     * 문제 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteQuiz(Long quizId, String deleteId) {
        log.debug("Deleting quiz. ID: {}, deleteId: {}", quizId, deleteId);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        // 기존 문제 존재 여부 확인
        findQuizById(quizId);

        // 1. quiz_content 테이블 삭제 (논리 삭제)
        quizContentMapper.deleteQuizContentsByQuizId(quizId, deleteId);

        // 2. quiz_master 테이블 삭제 (논리 삭제)
        int result = quizMapper.deleteQuiz(quizId, deleteId);
        if (result != 1) {
            throw new RuntimeException("문제 삭제에 실패했습니다.");
        }

        log.info("Quiz deleted successfully. ID: {}", quizId);
    }

    // ========== 유효성 검사 및 유틸리티 메서드 ==========

    /**
     * 문제 정보 유효성 검사
     * @param quiz 문제 정보
     */
    private void validateQuiz(Quiz quiz) {
        if (quiz == null) {
            throw new IllegalArgumentException("문제 정보는 필수입니다.");
        }

        if (quiz.getTitle() == null || quiz.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("문제 제목은 필수입니다.");
        }

        if (quiz.getCategoryId() == null) {
            throw new IllegalArgumentException("카테고리는 필수입니다.");
        }

        if (quiz.getQuizType() == null || quiz.getQuizType().trim().isEmpty()) {
            throw new IllegalArgumentException("문제 유형은 필수입니다.");
        }
        
        // 정답 검증은 향후 구현 예정
        // if (quiz.getCorrectAnswer() == null || quiz.getCorrectAnswer().trim().isEmpty()) {
        //     throw new IllegalArgumentException("정답은 필수입니다.");
        // }
    }

    /**
     * 문제명 중복 체크
     * @param question 문제 내용
     * @param excludeQuizId 제외할 문제 ID (수정 시 사용)
     * @return 중복 여부
     */
    private boolean isDuplicateQuestion(String question, Long excludeQuizId) {
        if (question == null || question.trim().isEmpty()) {
            return false;
        }
        
        int count = quizMapper.countDuplicateQuestion(question.trim(), excludeQuizId);
        return count > 0;
    }
}
