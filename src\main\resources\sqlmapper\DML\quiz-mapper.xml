<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QuizMapper">

    <resultMap id="QuizResultMap" type="kr.wayplus.qr_hallimpark.model.Quiz">
        <id property="quizId" column="quiz_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="title" column="title"/>
        <result property="quizType" column="quiz_type"/>
        <result property="status" column="status"/>
        <result property="difficultyLevel" column="difficulty_level"/>
        <result property="correctAnswer" column="correct_answer"/>
        <result property="imageUrl" column="image_url"/>
        <result property="hint" column="hint"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="question" column="question"/>
        <result property="options" column="options"/>
        <result property="categoryName" column="category_name"/>
        <result property="parentCategoryName" column="parent_category_name"/>
        <result property="categoryPath" column="category_path"/>
    </resultMap>

    <!-- 공통 검색 조건 -->
    <sql id="apiSearchConditions">
        <if test="searchKeyword != null and searchKeyword.trim() != ''">
            <choose>
                <when test="selectedSearchField == 'all' or mappedSearchField == null">
                    <!-- 전체 검색 - 주요 필드들에서 검색 -->
                    AND (qm.title LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR qm.quiz_type LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR qc.question LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%'))
                </when>
                <otherwise>
                    <!-- 단일 필드 검색 - Java에서 매핑된 필드 사용 -->
                    AND ${mappedSearchField} LIKE CONCAT('%', #{searchKeyword}, '%')
                </otherwise>
            </choose>
        </if>
    </sql>

    <!-- 공통 정렬 조건 -->
    <sql id="apiOrderBy">
        <choose>
            <when test="mappedSortField != null and sortDirection != null">
                ORDER BY ${mappedSortField} ${sortDirection}
            </when>
            <otherwise>
                ORDER BY qm.quiz_id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 활성화된 문제 목록 조회 -->
    <select id="selectActiveQuizzesForApi" parameterType="map" resultMap="QuizResultMap">
        SELECT
            qm.quiz_id,
            qm.category_id,
            qm.title,
            qm.quiz_type,
            qm.status,
            qm.difficulty_level,
            qm.correct_answer,
            qm.image_url,
            qm.hint,
            qm.create_id,
            qm.create_date,
            qm.last_update_id,
            qm.last_update_date,
            qm.delete_yn,
            qm.delete_id,
            qm.delete_date,
            qc.question,
            qc.options,
            cat.category_name,
            parent_cat.category_name AS parent_category_name,
            CASE
                WHEN parent_cat.category_name IS NOT NULL
                THEN CONCAT(parent_cat.category_name, ' > ', cat.category_name)
                ELSE cat.category_name
            END AS category_path
        FROM quiz_master qm
        INNER JOIN content_category cat ON qm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN quiz_content qc ON qm.quiz_id = qc.quiz_id AND qc.lang_code = 'ko' AND qc.delete_yn = 'N'
        LEFT JOIN content_category parent_cat ON cat.parent_id = parent_cat.category_id
        WHERE qm.delete_yn = 'N'
        AND qm.status = 'ACTIVE'

        <!-- 카테고리 필터 -->
        <if test="categoryId != null">
            AND qm.category_id = #{categoryId}
        </if>

        <!-- 검색 조건 적용 -->
        <include refid="apiSearchConditions"/>

        <!-- 정렬 적용 -->
        <include refid="apiOrderBy"/>

        <!-- 페이징 -->
        <if test="offset != null and size != null">
            LIMIT #{size} OFFSET #{offset}
        </if>
    </select>

    <!-- 활성화된 문제 총 개수 조회 -->
    <select id="countActiveQuizzesForApi" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM quiz_master qm
        INNER JOIN content_category cat ON qm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN quiz_content qc ON qm.quiz_id = qc.quiz_id AND qc.lang_code = 'ko' AND qc.delete_yn = 'N'
        LEFT JOIN content_category parent_cat ON cat.parent_id = parent_cat.category_id
        WHERE qm.delete_yn = 'N'
        AND qm.status = 'ACTIVE'

        <!-- 카테고리 필터 -->
        <if test="categoryId != null">
            AND qm.category_id = #{categoryId}
        </if>

        <!-- 검색 조건 적용 -->
        <include refid="apiSearchConditions"/>
    </select>

</mapper>
