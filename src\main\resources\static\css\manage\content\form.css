/* 한림공원 QR 체험 - 콘텐츠 등록/수정 폼 스타일 */

/* 카드 스타일 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
    border-bottom: none;
}

.card-header .card-title {
    font-weight: 600;
    font-size: 1.2rem;
    margin: 0;
}

.card-header i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.card-body {
    padding: 2rem;
}

/* 폼 요소 스타일 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label .text-danger {
    font-size: 0.9rem;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.invalid-feedback {
    font-size: 0.85rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* 필수 필드 표시 */
.text-danger {
    color: #dc3545 !important;
}

/* 버튼 스타일 */
.btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: none;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

/* 버튼 그룹 */
.d-flex.gap-2 {
    gap: 0.75rem !important;
}

/* 비활성화된 select */
.form-select:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
    cursor: not-allowed;
}

/* 폼 유효성 검사 스타일 */
.was-validated .form-control:valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.was-validated .form-select:valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated .form-select:invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .form-control,
    .form-select {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .card-header {
        padding: 1.2rem;
    }
    
    .card-header .card-title {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .d-flex.justify-content-end {
        flex-direction: column;
    }
    
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn:last-child {
        margin-bottom: 0;
    }
}

/* 로딩 상태 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn .fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 폼 섹션 구분 */
.row + .row {
    margin-top: 0.5rem;
}

/* 카테고리 선택 관련 스타일 */
#subCategoryId:disabled {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* 콘텐츠 코드 입력 필드 */
#contentCode {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* 알림 메시지 개선 */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert .btn-close {
    padding: 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
}

/* 페이지 헤더 버튼 스타일 */
.content-page-header .btn-secondary {
    background-color: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.content-page-header .btn-secondary:hover {
    background-color: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
