/* 한림공원 QR 체험 - 콘텐츠 관리 리스트 페이지 전용 스타일 */

/* 콘텐츠 리스트 컨테이너 */
.content-list-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    overflow: hidden;
    margin-bottom: 2rem;
}

/* 테이블 헤더 스타일 */
.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 1rem 0.75rem;
    vertical-align: middle;
    text-align: center;
}

.table thead th:first-child {
    border-top-left-radius: 10px;
}

.table thead th:last-child {
    border-top-right-radius: 10px;
}

/* 테이블 바디 스타일 */
.table tbody td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
    font-size: 0.9rem;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 콘텐츠 제목 셀 특별 스타일 */
.content-title-cell {
    text-align: left;
    max-width: 200px;
}

.content-title-cell strong {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    line-height: 1.3;
    word-break: break-word;
}

.content-title-cell small {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 400;
}

/* 콘텐츠 코드 스타일 */
.content-code {
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    border: 1px solid #dee2e6;
    display: inline-block;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 카테고리 경로 스타일 */
.category-path {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

/* 접근 권한 배지 스타일 */
.access-level-badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 12px;
    font-weight: 500;
}

.access-level-badge.bg-info {
    background-color: #17a2b8 !important;
    color: white;
}

.access-level-badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

.access-level-badge.bg-danger {
    background-color: #dc3545 !important;
    color: white;
}

/* 상태 토글 버튼 개선 */
.status-toggle-btn {
    min-width: 70px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 15px;
    padding: 0.4rem 0.8rem;
}

.status-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-toggle-btn.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.status-toggle-btn.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.status-toggle-btn.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.status-toggle-btn.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

/* 문제 연결 배지 스타일 */
.quiz-count-badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 12px;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.quiz-count-badge.bg-primary {
    background-color: #007bff !important;
    color: white;
}

.quiz-count-badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
}

/* 관리 버튼 그룹 개선 */
.content-actions {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.content-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 개별 액션 버튼 스타일 */
.content-detail-btn {
    color: #007bff;
    border-color: #007bff;
    background-color: transparent;
}

.content-detail-btn:hover {
    background-color: #007bff;
    color: white;
}

.content-edit-btn {
    color: #ffc107;
    border-color: #ffc107;
    background-color: transparent;
}

.content-edit-btn:hover {
    background-color: #ffc107;
    color: #212529;
}

.content-delete-btn {
    color: #dc3545;
    border-color: #dc3545;
    background-color: transparent;
}

.content-delete-btn:hover {
    background-color: #dc3545;
    color: white;
}

/* 날짜 표시 스타일 */
.date-display {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 400;
}

/* 반응형 디자인 */
@media (max-width: 1200px) {
    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
        font-size: 0.85rem;
    }
    
    .content-title-cell {
        max-width: 150px;
    }
    
    .category-path {
        max-width: 120px;
    }
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .content-title-cell {
        max-width: 120px;
    }
    
    .content-title-cell strong {
        font-size: 0.85rem;
    }
    
    .content-code {
        max-width: 80px;
        font-size: 0.75rem;
    }
    
    .category-path {
        max-width: 100px;
        font-size: 0.8rem;
    }
    
    .content-actions .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
        min-width: 28px;
        height: 28px;
    }
    
    .status-toggle-btn {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
        min-width: 60px;
    }
}

/* 로딩 및 빈 상태 개선 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1.5rem;
    display: block;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.empty-state p {
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
}

/* 테이블 스크롤 개선 */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
