package kr.wayplus.qr_hallimpark.controller.manage;

import kr.wayplus.qr_hallimpark.enums.ContentSearchField;
import kr.wayplus.qr_hallimpark.enums.ContentSortField;
import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.Content;
import kr.wayplus.qr_hallimpark.model.ContentCategory;
import kr.wayplus.qr_hallimpark.service.ContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 콘텐츠 관리 컨트롤러
 * - 관리자 콘텐츠 관리 페이지 처리
 * - 콘텐츠 CRUD 및 검색 기능 제공
 */
@Slf4j
@Controller
@RequestMapping("/manage/content")
@RequiredArgsConstructor
public class ManageContentController {
    
    private final ContentService contentService;

    /**
     * 콘텐츠 관리 메인 페이지 (검색, 정렬, 페이징 기능 포함)
     * @param page 페이지 번호 (기본값: 1)
     * @param size 페이지 크기 (기본값: 20, 최대: 100)
     * @param categoryId 카테고리 ID (선택사항)
     * @param searchKeyword 검색 키워드 (선택사항)
     * @param selectedSearchField 검색 필드 (title: 콘텐츠 제목, content_code: 콘텐츠 코드, category_name: 카테고리명, all: 전체)
     * @param sortField 정렬 필드 (기본값: content_id)
     * @param sortDirection 정렬 방향 (ASC: 오름차순, DESC: 내림차순, 기본값: DESC)
     * @param status 상태 필터 (ACTIVE: 활성, INACTIVE: 비활성, 선택사항)
     * @param accessLevel 접근 권한 필터 (ALL: 전체, SUPER_ADMIN: 슈퍼 관리자, NORMAL_ADMIN: 일반 관리자, 선택사항)
     * @return 콘텐츠 목록 페이지
     */
    @GetMapping("/list")
    public ModelAndView contentList(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @RequestParam(value = "selectedSearchField", defaultValue = "all") String selectedSearchField,
            @RequestParam(value = "sortField", defaultValue = "content_id") String sortField,
            @RequestParam(value = "sortDirection", defaultValue = "DESC") String sortDirection,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "accessLevel", required = false) String accessLevel) {

        log.debug("Content management page requested - page: {}, size: {}, categoryId: {}, searchKeyword: {}, selectedSearchField: {}, sortField: {}, sortDirection: {}, status: {}, accessLevel: {}",
                page, size, categoryId, searchKeyword, selectedSearchField, sortField, sortDirection, status, accessLevel);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ModelAndView modelAndView = new ModelAndView("manage/content/list");

        try {
            // 페이지 및 크기 유효성 검사
            if (page < 1) {
                page = 1;
            }
            if (size < 1) {
                size = 20;
            }
            if (size > 100) {
                size = 100; // 최대 100개로 제한
            }

            // 검색 필드 매핑 (enum 사용)
            ContentSearchField searchFieldEnum = ContentSearchField.findByFieldName(selectedSearchField);
            List<String> searchFields = new ArrayList<>();

            if (searchFieldEnum != null) {
                if (searchFieldEnum.isAllSearch()) {
                    // 전체 검색인 경우 주요 필드들 포함
                    searchFields.addAll(Arrays.asList("title", "content_code", "category_name"));
                } else {
                    // 특정 필드 검색인 경우 해당 필드만
                    if (searchFieldEnum.getDbColumn() != null) {
                        searchFields.add(searchFieldEnum.getDbColumn().replace("cm.", "").replace("cat.", "").replace("sub_cat.", ""));
                    }
                }
            } else {
                // 기본값: 전체 검색
                searchFields.addAll(Arrays.asList("title", "content_code", "category_name"));
            }

            // 정렬 필드 유효성 검사 및 매핑 (enum 사용)
            ContentSortField sortFieldEnum = ContentSortField.findByFieldKey(sortField);
            String validSortField = sortFieldEnum.getFieldKey();

            // 정렬 방향 유효성 검사
            String validSortDirection = "DESC";
            if ("ASC".equalsIgnoreCase(sortDirection) || "DESC".equalsIgnoreCase(sortDirection)) {
                validSortDirection = sortDirection.toUpperCase();
            }

            // 검색 조건 생성
            AdminListSearch searchCondition = AdminListSearch.builder()
                    .page(page)
                    .size(size)
                    .searchKeyword(searchKeyword)
                    .selectedSearchField(selectedSearchField)
                    .searchFields(searchFields)
                    .sortField(validSortField)
                    .sortDirection(validSortDirection)
                    .categoryId(categoryId)
                    .status(status)
                    .includeDeleted(false)
                    .tableName("content_master")
                    .build();

            // 접근 권한 필터 추가
            if (accessLevel != null && !accessLevel.trim().isEmpty()) {
                searchCondition.getFilters().put("access_level", accessLevel);
            }

            log.debug("Search condition created - page: {}, size: {}, searchKeyword: {}, selectedSearchField: {}, searchFields: {}, sortField: {}, sortDirection: {}, categoryId: {}, status: {}, accessLevel: {}",
                    searchCondition.getPage(), searchCondition.getSize(), searchCondition.getSearchKeyword(),
                    searchCondition.getSelectedSearchField(), searchCondition.getSearchFields(),
                    searchCondition.getSortField(), searchCondition.getSortDirection(),
                    searchCondition.getCategoryId(), searchCondition.getStatus(), accessLevel);

            // 데이터 조회
            AdminListResponse<Content> listResponse = contentService.findListWithConditions(searchCondition);

            // 최상위 카테고리 목록 조회
            List<ContentCategory> rootCategories = contentService.findRootCategoriesForContentManagement();

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "콘텐츠 관리");
            modelAndView.addObject("pageDescription", "콘텐츠를 관리합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("searchCondition", searchCondition);
            modelAndView.addObject("listResponse", listResponse);
            modelAndView.addObject("rootCategories", rootCategories);

            // 검색 필드 옵션 추가 (enum 사용)
            modelAndView.addObject("searchFieldOptions", ContentSearchField.getAllowedFieldNames());

            // 정렬 필드 옵션 추가 (enum 사용)
            modelAndView.addObject("sortFieldOptions", ContentSortField.getAllowedSortFields());

            // 상태 필터 옵션 추가
            HashMap<String, String> statusOptions = new HashMap<>();
            statusOptions.put("", "전체");
            statusOptions.put("ACTIVE", "활성");
            statusOptions.put("INACTIVE", "비활성");
            modelAndView.addObject("statusOptions", statusOptions);

            // 접근 권한 필터 옵션 추가
            HashMap<String, String> accessLevelOptions = new HashMap<>();
            accessLevelOptions.put("", "전체");
            accessLevelOptions.put("ALL", "전체 접근");
            accessLevelOptions.put("SUPER_ADMIN", "슈퍼 관리자");
            accessLevelOptions.put("NORMAL_ADMIN", "일반 관리자");
            modelAndView.addObject("accessLevelOptions", accessLevelOptions);

        } catch (Exception e) {
            log.error("Error loading content list: {}", e.getMessage(), e);
            modelAndView.addObject("errorMessage", "콘텐츠 목록을 불러오는 중 오류가 발생했습니다.");
        }

        return modelAndView;
    }

    /**
     * 콘텐츠 검색 (AJAX)
     */
    @PostMapping("/search")
    @ResponseBody
    public AdminListResponse<Content> searchContents(@RequestBody AdminListSearch searchCondition) {
        log.debug("Content search requested with condition: {}", searchCondition);

        try {
            log.debug("Received search condition - selectedSearchField: {}, searchFields: {}, searchKeyword: {}",
                        searchCondition.getSelectedSearchField(),
                        searchCondition.getSearchFields(),
                        searchCondition.getSearchKeyword());

            // JavaScript에서 이미 searchFields를 설정해서 보내므로 별도 처리 불필요
            // 하지만 안전을 위해 기본값 설정
            if (searchCondition.getSearchFields() == null || searchCondition.getSearchFields().isEmpty()) {
                searchCondition.setSearchFields(Arrays.asList("title", "content_code", "category_name"));
            }

            // 구조화된 조건 설정
            searchCondition.setIncludeDeleted(false);
            searchCondition.setTableName("content_master");

            return contentService.findListWithConditions(searchCondition);

        } catch (Exception e) {
            log.error("Error searching contents: {}", e.getMessage(), e);
            return AdminListResponse.error("콘텐츠 검색 중 오류가 발생했습니다: " + e.getMessage(), "SEARCH_ERROR");
        }
    }

    /**
     * 특정 카테고리의 콘텐츠 목록 조회 (AJAX)
     */
    @GetMapping("/category/{categoryId}")
    @ResponseBody
    public HashMap<String, Object> getContentsByCategory(@PathVariable("categoryId") Long categoryId) {
        log.debug("Getting contents by category ID: {}", categoryId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<Content> contents = contentService.findContentsWithChildCategories(categoryId);

            response.put("success", true);
            response.put("data", contents);
            response.put("message", "카테고리별 콘텐츠 목록을 성공적으로 조회했습니다.");

        } catch (Exception e) {
            log.error("Error getting contents by category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리별 콘텐츠 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 하위 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/category/{parentId}/children")
    @ResponseBody
    public HashMap<String, Object> getChildCategories(@PathVariable("parentId") Long parentId) {
        log.debug("Getting child categories for parent ID: {}", parentId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<ContentCategory> childCategories = contentService.findChildCategories(parentId);
            response.put("success", true);
            response.put("data", childCategories);
            response.put("message", "하위 카테고리 목록을 성공적으로 조회했습니다.");

        } catch (Exception e) {
            log.error("Error getting child categories: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "하위 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 콘텐츠 상세 조회 (AJAX)
     */
    @GetMapping("/{contentId}/detail")
    @ResponseBody
    public HashMap<String, Object> getContent(@PathVariable("contentId") Long contentId) {
        log.debug("Getting content. ID: {}", contentId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            Content content = contentService.findContentById(contentId);

            response.put("success", true);
            response.put("data", content);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid content ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error getting content: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "콘텐츠 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 최상위 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/root-categories")
    @ResponseBody
    public HashMap<String, Object> getRootCategories() {
        log.debug("Getting root categories for content management");

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<ContentCategory> rootCategories = contentService.findRootCategoriesForContentManagement();

            response.put("success", true);
            response.put("data", rootCategories);

        } catch (Exception e) {
            log.error("Error getting root categories: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "최상위 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 콘텐츠 등록 페이지
     */
    @GetMapping("/new")
    public ModelAndView contentForm() {
        log.debug("Content form page requested");

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ModelAndView modelAndView = new ModelAndView("manage/content/form");

        try {
            // 최상위 카테고리 목록 조회
            List<ContentCategory> rootCategories = contentService.findRootCategoriesForContentManagement();

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "콘텐츠 등록");
            modelAndView.addObject("pageDescription", "새로운 콘텐츠를 등록합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("isEdit", false);
            modelAndView.addObject("rootCategories", rootCategories);

            // 빈 Content 객체 생성 (기본값 설정)
            Content content = Content.builder()
                    .status("ACTIVE")
                    .accessLevel("ALL")
                    .deleteYn("N")
                    .build();
            modelAndView.addObject("content", content);

        } catch (Exception e) {
            log.error("Error loading content form: {}", e.getMessage(), e);
            modelAndView.addObject("errorMessage", "콘텐츠 등록 페이지를 불러오는 중 오류가 발생했습니다.");
        }

        return modelAndView;
    }

    /**
     * 콘텐츠 수정 페이지
     */
    @GetMapping("/{contentId}/edit")
    public ModelAndView contentEditForm(@PathVariable Long contentId) {
        log.debug("Content edit form page requested. Content ID: {}", contentId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        try {
            // 기존 콘텐츠 정보 조회
            Content content = contentService.findContentById(contentId);

            // 최상위 카테고리 목록 조회
            List<ContentCategory> rootCategories = contentService.findRootCategoriesForContentManagement();

            ModelAndView modelAndView = new ModelAndView("manage/content/form");

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "콘텐츠 수정");
            modelAndView.addObject("pageDescription", "콘텐츠 정보를 수정합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("content", content);
            modelAndView.addObject("isEdit", true);
            modelAndView.addObject("contentId", contentId);
            modelAndView.addObject("rootCategories", rootCategories);

            return modelAndView;

        } catch (IllegalArgumentException e) {
            log.warn("Invalid content ID: {}", e.getMessage());
            return new ModelAndView("redirect:/manage/content/list?error=invalid_content");
        } catch (Exception e) {
            log.error("Error loading content edit form: {}", e.getMessage(), e);
            return new ModelAndView("redirect:/manage/content/list?error=load_failed");
        }
    }

    // ========== 향후 구현할 CRUD 메서드들 ==========

    /**
     * 콘텐츠 등록 처리 (AJAX) - 향후 구현
     */
    @PostMapping("/add")
    @ResponseBody
    public HashMap<String, Object> createContent(@RequestBody Content content) {
        log.debug("Creating content: {}", content);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 생성자 정보 설정
            content.setCreateId(auth.getName());

            // 콘텐츠 등록
            contentService.createContent(content);

            response.put("success", true);
            response.put("message", "콘텐츠가 성공적으로 등록되었습니다.");

        } catch (IllegalArgumentException e) {
            log.warn("Invalid content data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error creating content: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "콘텐츠 등록 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 콘텐츠 수정 처리 (AJAX) - 향후 구현
     */
    @PostMapping("/{contentId}")
    @ResponseBody
    public HashMap<String, Object> updateContent(@PathVariable Long contentId, @RequestBody Content content) {
        log.debug("Updating content. ID: {}, Data: {}", contentId, content);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 수정자 정보 설정
            content.setContentId(contentId);
            content.setLastUpdateId(auth.getName());

            // 콘텐츠 수정
            contentService.updateContent(content);

            response.put("success", true);
            response.put("message", "콘텐츠가 성공적으로 수정되었습니다.");

        } catch (IllegalArgumentException e) {
            log.warn("Invalid content data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error updating content: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "콘텐츠 수정 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 콘텐츠 삭제 처리 (AJAX) - 향후 구현
     */
    @DeleteMapping("/{contentId}")
    @ResponseBody
    public HashMap<String, Object> deleteContent(@PathVariable Long contentId) {
        log.debug("Deleting content. ID: {}", contentId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 콘텐츠 삭제
            contentService.deleteContent(contentId, auth.getName());

            response.put("success", true);
            response.put("message", "콘텐츠가 성공적으로 삭제되었습니다.");

        } catch (IllegalArgumentException e) {
            log.warn("Invalid content ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error deleting content: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "콘텐츠 삭제 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 콘텐츠 상태 토글 (AJAX)
     */
    @PostMapping("/{contentId}/toggle-status")
    @ResponseBody
    public HashMap<String, Object> toggleContentStatus(@PathVariable Long contentId) {
        log.debug("Toggling content status. ID: {}", contentId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 현재 콘텐츠 정보 조회
            Content currentContent = contentService.findContentById(contentId);

            // 상태 토글
            String newStatus = "ACTIVE".equals(currentContent.getStatus()) ? "INACTIVE" : "ACTIVE";

            // 상태 업데이트
            contentService.updateContentStatus(contentId, newStatus, auth.getName());

            response.put("success", true);
            response.put("newStatus", newStatus);
            response.put("statusDisplay", "ACTIVE".equals(newStatus) ? "활성" : "비활성");
            response.put("message", "콘텐츠 상태가 성공적으로 변경되었습니다.");

        } catch (Exception e) {
            log.error("Error toggling content status: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", e.getMessage());
        }

        return response;
    }
}
