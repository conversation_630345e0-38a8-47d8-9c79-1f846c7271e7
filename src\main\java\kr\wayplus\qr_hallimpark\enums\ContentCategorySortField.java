package kr.wayplus.qr_hallimpark.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 콘텐츠 카테고리 정렬 필드 enum
 * - 정렬 가능한 필드와 표시명을 관리
 */
@Getter
@RequiredArgsConstructor
public enum ContentCategorySortField {
    
    CATEGORY_ID("category_id", "등록순"),
    CATEGORY_NAME("category_name", "카테고리명"),
    CREATE_DATE("create_date", "생성일"),
    LAST_UPDATE_DATE("last_update_date", "수정일"),
    PARENT_ID("parent_id", "부모 카테고리");
    
    private final String fieldKey;
    private final String displayName;
    
    /**
     * 필드 키로 enum 찾기
     * @param fieldKey 필드 키
     * @return 해당하는 enum, 없으면 CATEGORY_ID 반환
     */
    public static ContentCategorySortField fromFieldKey(String fieldKey) {
        if (fieldKey == null) {
            return CATEGORY_ID;
        }
        
        return Arrays.stream(values())
                .filter(field -> field.getFieldKey().equals(fieldKey))
                .findFirst()
                .orElse(CATEGORY_ID);
    }
    
    /**
     * 모든 정렬 필드 옵션을 Map 형태로 반환
     * @return 필드키:표시명 형태의 Map
     */
    public static java.util.Map<String, String> getAllOptions() {
        java.util.Map<String, String> options = new java.util.LinkedHashMap<>();
        for (ContentCategorySortField field : values()) {
            options.put(field.getFieldKey(), field.getDisplayName());
        }
        return options;
    }
}
