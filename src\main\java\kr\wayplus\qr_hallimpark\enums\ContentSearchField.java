package kr.wayplus.qr_hallimpark.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 콘텐츠 검색 필드 정의
 * - 검색 가능한 필드들을 체계적으로 관리
 * - 필드 추가/수정이 매우 간단함
 */
@Getter
@RequiredArgsConstructor
public enum ContentSearchField {

    // 프론트엔드에서 사용 가능한 검색 필드들
    ALL("all", null, "전체", true),
    TITLE("title", "cm.title", "콘텐츠 제목", true),
    CONTENT_CODE("content_code", "cm.content_code", "콘텐츠 코드", true),
    CATEGORY_NAME("category_name", "cat.category_name", "카테고리명", false),
    SUB_CATEGORY_NAME("sub_category_name", "sub_cat.category_name", "하위 카테고리명", false),
    ACCESS_LEVEL("access_level", "cm.access_level", "접근 권한", false),
    CREATE_ID("create_id", "cm.create_id", "생성자", false),
    STATUS("status", "cm.status", "상태", false),
    CREATE_DATE("create_date", "cm.create_date", "생성일", false),
    LAST_UPDATE_DATE("last_update_date", "cm.last_update_date", "수정일", false);

    private final String fieldName;      // 검색필드명
    private final String dbColumn;      // 데이터베이스 컬럼명
    private final String description;   
    private final boolean isFront;       // 프론트엔드에서 사용 가능 여부
    
    /**
     * 필드 키로 검색 필드 찾기
     * @param fieldName 필드 키
     * @return 검색 필드 (없으면 null)
     */
    public static ContentSearchField findByFieldName(String fieldName) {
        if (fieldName == null) {
            return ALL;
        }
        
        for (ContentSearchField field : values()) {
            if (field.fieldName.equals(fieldName)) {
                return field;
            }
        }
        return null;
    }
    
    /**
     * 유효한 검색 필드인지 확인
     * @param fieldName 필드 키
     * @return 유효 여부
     */
    public static boolean isValidField(String fieldName) {
        return findByFieldName(fieldName) != null;
    }
    
    /**
     * 전체 검색 필드인지 확인
     * @return 전체 검색 여부
     */
    public boolean isAllSearch() {
        return this == ALL;
    }
    
    /**
     * 프론트엔드용 허용된 검색 필드 Map 반환 (한국어 설명: 필드키)
     * @return 허용된 필드 Map (description: fieldName)
     */
    public static java.util.Map<String, String> getAllowedFieldNames() {
        java.util.Map<String, String> fieldMap = new java.util.LinkedHashMap<>();
        java.util.Arrays.stream(values())
                .filter(field -> field.isFront)
                .forEach(field -> fieldMap.put(field.getDescription(), field.getFieldName()));
        return fieldMap;
    }
}
