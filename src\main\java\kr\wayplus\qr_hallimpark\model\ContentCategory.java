package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.ArrayList;

/**
 * 콘텐츠 카테고리 모델
 * - content_category 테이블에 대응
 * - 계층형 구조 지원 (부모-자식 관계)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContentCategory {

    private Long categoryId;
    private Long parentId;
    private String categoryName;
    private String description;
    private String createId;
    private java.time.LocalDateTime createDate;
    private String lastUpdateId;
    private java.time.LocalDateTime lastUpdateDate;
    private String deleteYn;
    private String deleteId;
    private java.time.LocalDateTime deleteDate;
    private ContentCategory parent;

    @Builder.Default
    private List<ContentCategory> children = new ArrayList<>();
    private String parentCategoryName;
    private Integer depth;
    private String categoryPath;
    private Integer childrenCount;
    // ========== 계층형 구조 관련 유틸리티 메서드 ==========

    /**
     * 최상위 카테고리인지 확인
     * @return 최상위 카테고리 여부
     */
    public boolean isRootCategory() {
        return this.parentId == null;
    }

    /**
     * 자식 카테고리가 있는지 확인
     * @return 자식 카테고리 존재 여부
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }

    /**
     * 자식 카테고리 추가
     * @param child 자식 카테고리
     */
    public void addChild(ContentCategory child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
        child.setParent(this);
    }
}
