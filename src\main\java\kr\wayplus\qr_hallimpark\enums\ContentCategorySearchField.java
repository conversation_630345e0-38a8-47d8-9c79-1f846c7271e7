package kr.wayplus.qr_hallimpark.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 콘텐츠 카테고리 검색 필드 enum
 * - 검색 가능한 필드와 표시명을 관리
 */
@Getter
@RequiredArgsConstructor
public enum ContentCategorySearchField {
    
    ALL("all", "전체", Arrays.asList("category_name", "description")),
    CATEGORY_NAME("category_name", "카테고리명", Arrays.asList("category_name")),
    DESCRIPTION("description", "설명", Arrays.asList("description"));
    
    private final String fieldKey;
    private final String displayName;
    private final List<String> dbColumns;
    
    /**
     * 필드 키로 enum 찾기
     * @param fieldKey 필드 키
     * @return 해당하는 enum, 없으면 ALL 반환
     */
    public static ContentCategorySearchField fromFieldKey(String fieldKey) {
        if (fieldKey == null) {
            return ALL;
        }
        
        return Arrays.stream(values())
                .filter(field -> field.getFieldKey().equals(fieldKey))
                .findFirst()
                .orElse(ALL);
    }
    
    /**
     * 모든 검색 필드 옵션을 Map 형태로 반환
     * @return 필드키:표시명 형태의 Map
     */
    public static java.util.Map<String, String> getAllOptions() {
        java.util.Map<String, String> options = new java.util.LinkedHashMap<>();
        for (ContentCategorySearchField field : values()) {
            options.put(field.getFieldKey(), field.getDisplayName());
        }
        return options;
    }
}
