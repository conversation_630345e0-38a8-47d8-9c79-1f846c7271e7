/**
 * 콘텐츠 카테고리 관리 JavaScript
 * - 콘텐츠 카테고리 CRUD 기능 처리
 */
const ContentCategoryManager = {
    
    /**
     * 초기화
     */
    init() {
        console.log('ContentCategoryManager initialized');
        this.bindEvents();
    },
    
    /**
     * 폼 초기화
     */
    initForm(isEdit = false, categoryData = null) {
        console.log('ContentCategoryManager form initialized', { isEdit, categoryData });
        this.bindFormEvents();
        this.setupFormValidation();
        
        if (isEdit && categoryData) {
            this.populateForm(categoryData);
        }
        
        this.updatePreview();
    },
    
    /**
     * 이벤트 바인딩
     */
    bindEvents() {
        // 전역 이벤트 바인딩
    },
    
    /**
     * 폼 이벤트 바인딩
     */
    bindFormEvents() {
        const categoryNameInput = document.querySelector('[data-category-input]');
        const descriptionTextarea = document.querySelector('[data-description-input]');
        const form = document.getElementById('categoryForm');

        if (categoryNameInput) {
            categoryNameInput.addEventListener('input', () => this.updatePreview());
            categoryNameInput.addEventListener('input', () => this.validateCategoryName());
        }

        if (descriptionTextarea) {
            descriptionTextarea.addEventListener('input', () => this.updatePreview());
            descriptionTextarea.addEventListener('input', () => this.validateDescription());
        }

        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    },
    
    /**
     * 폼 유효성 검사 설정
     */
    setupFormValidation() {
        const form = document.getElementById('categoryForm');
        if (!form) return;
        
        // Bootstrap 유효성 검사 클래스 추가
        form.classList.add('needs-validation');
        form.noValidate = true;
    },
    
    /**
     * 폼 데이터 채우기 (수정 모드)
     */
    populateForm(categoryData) {
        const categoryNameInput = document.querySelector('[data-category-input]');
        const descriptionTextarea = document.querySelector('[data-description-input]');

        if (categoryNameInput && categoryData.categoryName) {
            categoryNameInput.value = categoryData.categoryName;
        }

        if (descriptionTextarea && categoryData.description) {
            descriptionTextarea.value = categoryData.description;
        }
    },
    
    /**
     * 미리보기 업데이트
     */
    updatePreview() {
        const categoryNameInput = document.querySelector('[data-category-input]');
        const descriptionTextarea = document.querySelector('[data-description-input]');
        const preview = document.querySelector('[data-preview-container]');
        const previewName = document.querySelector('[data-preview-name]');
        const previewDescription = document.querySelector('[data-preview-description]');

        if (!categoryNameInput || !preview || !previewName || !previewDescription) return;

        const categoryName = categoryNameInput.value.trim();
        const description = descriptionTextarea ? descriptionTextarea.value.trim() : '';

        if (categoryName) {
            previewName.textContent = categoryName;
            previewDescription.textContent = description || '설명이 없습니다.';
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    },
    
    /**
     * 카테고리명 유효성 검사
     */
    validateCategoryName() {
        const categoryNameInput = document.querySelector('[data-category-input]');
        if (!categoryNameInput) return true;

        const categoryName = categoryNameInput.value.trim();
        const isValid = categoryName.length > 0 && categoryName.length <= 100;

        if (isValid) {
            categoryNameInput.classList.remove('is-invalid');
            categoryNameInput.classList.add('is-valid');
        } else {
            categoryNameInput.classList.remove('is-valid');
            categoryNameInput.classList.add('is-invalid');
        }

        return isValid;
    },

    /**
     * 설명 유효성 검사
     */
    validateDescription() {
        const descriptionTextarea = document.querySelector('[data-description-input]');
        if (!descriptionTextarea) return true;

        const description = descriptionTextarea.value;
        const isValid = description.length <= 1000;

        if (isValid) {
            descriptionTextarea.classList.remove('is-invalid');
            descriptionTextarea.classList.add('is-valid');
        } else {
            descriptionTextarea.classList.remove('is-valid');
            descriptionTextarea.classList.add('is-invalid');
        }

        return isValid;
    },
    
    /**
     * 폼 제출 처리
     */
    async handleFormSubmit(event) {
        event.preventDefault();
        
        const form = event.target;
        const submitBtn = document.querySelector('[data-submit-btn]');
        
        // 유효성 검사
        if (!this.validateForm(form)) {
            form.classList.add('was-validated');
            return;
        }
        
        // 버튼 비활성화
        this.setSubmitButtonLoading(submitBtn, true);
        
        try {
            const formData = new FormData(form);
            const data = {
                categoryName: formData.get('categoryName'),
                description: formData.get('description') || null
            };
            
            const url = form.action;
            const method = form.method.toUpperCase();
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert(result.message);
                window.location.href = '/manage/quiz-category/list';
            } else {
                alert(result.message);
            }
            
        } catch (error) {
            console.error('Form submission error:', error);
            alert('요청 처리 중 오류가 발생했습니다.');
        } finally {
            this.setSubmitButtonLoading(submitBtn, false);
        }
    },
    
    /**
     * 폼 유효성 검사
     */
    validateForm(form) {
        let isValid = true;

        // 카테고리명 검사
        if (!this.validateCategoryName()) {
            isValid = false;
        }

        // 설명 검사
        if (!this.validateDescription()) {
            isValid = false;
        }

        return isValid;
    },
    
    /**
     * 제출 버튼 로딩 상태 설정
     */
    setSubmitButtonLoading(button, isLoading) {
        if (!button) return;
        
        if (isLoading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>처리 중...';
        } else {
            button.disabled = false;
            const isEdit = button.textContent.includes('수정');
            button.innerHTML = isEdit ? 
                '<i class="fas fa-save me-2"></i>수정하기' : 
                '<i class="fas fa-plus me-2"></i>등록하기';
        }
    },
    
    /**
     * 카테고리 삭제
     */
    async deleteCategory(categoryId, categoryName) {
        if (!confirm(`'${categoryName}' 카테고리를 정말 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.`)) {
            return;
        }
        
        try {
            const response = await fetch(`/manage/quiz-category/${categoryId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert(result.message);
                window.location.reload();
            } else {
                alert(result.message);
            }
            
        } catch (error) {
            console.error('Delete error:', error);
            alert('삭제 처리 중 오류가 발생했습니다.');
        }
    }
};

/**
 * 전역 함수 - 카테고리 삭제 (템플릿에서 호출)
 */
function deleteCategory(categoryId, categoryName) {
    ContentCategoryManager.deleteCategory(categoryId, categoryName);
}
