package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

/**
 * 콘텐츠 모델
 * - content_master 테이블에 대응
 * - 콘텐츠 관리 페이지에서 사용되는 모델
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Content {

    // ========== content_master 테이블 필드 ==========
    
    /**
     * 콘텐츠 고유 ID
     */
    private Long contentId;

    /**
     * 콘텐츠 제목 또는 키워드명 (관리 및 식별용)
     */
    private String title;

    /**
     * 대분류 카테고리 ID (content_category FK, NULL 허용)
     */
    private Long categoryId;

    /**
     * 소분류 카테고리 ID (content_category FK, NULL 허용)
     */
    private Long subCategoryId;

    /**
     * 콘텐츠 고유번호 (외부 연동 또는 식별용 코드)
     */
    private String contentCode;

    /**
     * 콘텐츠 상태
     * - ACTIVE: 활성
     * - INACTIVE: 비활성
     */
    private String status;

    /**
     * 접근 권한
     * - ALL: 전체
     * - SUPER_ADMIN: 슈퍼 관리자
     * - NORMAL_ADMIN: 일반 관리자
     */
    private String accessLevel;

    /**
     * 생성자 (user_email)
     */
    private String createId;

    /**
     * 생성일시
     */
    private LocalDateTime createDate;

    /**
     * 최종수정자 (user_email)
     */
    private String lastUpdateId;

    /**
     * 최종수정일시
     */
    private LocalDateTime lastUpdateDate;

    /**
     * 삭제여부
     */
    private String deleteYn;

    /**
     * 삭제자 (user_email)
     */
    private String deleteId;

    /**
     * 삭제일시
     */
    private LocalDateTime deleteDate;

    // ========== 조인 및 계산 필드 ==========

    /**
     * 카테고리명
     */
    private String categoryName;

    /**
     * 하위 카테고리명
     */
    private String subCategoryName;

    /**
     * 카테고리 전체 경로 (대분류 > 소분류)
     */
    private String categoryPath;

    /**
     * 상태 표시용 (한국어)
     * - 계산된 필드 (DB 저장 X)
     */
    private String statusDisplay;

    /**
     * 접근 권한 표시용 (한국어)
     * - 계산된 필드 (DB 저장 X)
     */
    private String accessLevelDisplay;

    /**
     * 연결된 문제 개수
     * - 이 콘텐츠와 연결된 문제의 개수
     */
    private Integer quizMappingCount;

    // ========== 유틸리티 메서드 ==========

    /**
     * 활성 상태인지 확인
     * @return 활성 상태 여부
     */
    public boolean isActive() {
        return "N".equals(this.deleteYn);
    }

    /**
     * 상태를 한국어로 변환
     * @return 한국어 상태
     */
    public String getStatusDisplay() {
        if ("ACTIVE".equals(this.status)) {
            return "활성";
        } else if ("INACTIVE".equals(this.status)) {
            return "비활성";
        } else {
            // 삭제 여부로 판단 (기존 로직 유지)
            return isActive() ? "활성" : "비활성";
        }
    }

    /**
     * 접근 권한을 한국어로 변환
     * @return 한국어 접근 권한
     */
    public String getAccessLevelDisplay() {
        if (this.accessLevel == null) {
            return "전체";
        }
        
        switch (this.accessLevel) {
            case "ALL":
                return "전체";
            case "SUPER_ADMIN":
                return "슈퍼 관리자";
            case "NORMAL_ADMIN":
                return "일반 관리자";
            default:
                return this.accessLevel;
        }
    }

    /**
     * 하위 카테고리 여부 확인
     * @return 하위 카테고리 여부
     */
    public boolean hasSubCategory() {
        return this.subCategoryName != null && !this.subCategoryName.trim().isEmpty();
    }

    /**
     * 문제 연동 여부 확인
     * @return 문제 연동 여부
     */
    public boolean hasQuizMapping() {
        return this.quizMappingCount != null && this.quizMappingCount > 0;
    }

    /**
     * 카테고리 설정 여부 확인
     * @return 카테고리 설정 여부
     */
    public boolean hasCategory() {
        return this.categoryId != null;
    }
}
