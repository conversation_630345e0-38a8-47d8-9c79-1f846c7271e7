package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.model.Content;
import kr.wayplus.qr_hallimpark.model.ContentCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 콘텐츠 매퍼
 * - content_master, content_category 테이블 조인 처리
 * - 공통 리스트 기능 지원
 */
@Mapper
@Repository
public interface ContentMapper extends AdminListMapper<Content> {
    
    /**
     * 모든 콘텐츠 목록 조회 (카테고리 정보 포함)
     * @return 콘텐츠 목록
     */
    List<Content> selectContentList();
    
    /**
     * 콘텐츠 ID로 콘텐츠 조회
     * @param contentId 콘텐츠 ID
     * @return 콘텐츠 정보
     */
    Content selectContentById(Long contentId);
    
    /**
     * 콘텐츠 코드로 콘텐츠 조회
     * @param contentCode 콘텐츠 코드
     * @return 콘텐츠 정보
     */
    Content selectContentByCode(String contentCode);
    
    /**
     * 특정 카테고리의 콘텐츠 목록 조회
     * @param categoryId 카테고리 ID
     * @return 콘텐츠 목록
     */
    List<Content> selectContentListByCategoryId(Long categoryId);
    
    /**
     * 특정 카테고리와 그 하위 카테고리의 콘텐츠 목록 조회
     * @param categoryId 상위 카테고리 ID
     * @return 콘텐츠 목록
     */
    List<Content> selectContentListByCategoryIdWithChildren(Long categoryId);
    
    /**
     * 콘텐츠 등록
     * @param content 콘텐츠 정보
     * @return 등록된 행 수
     */
    int insertContent(Content content);
    
    /**
     * 콘텐츠 수정
     * @param content 콘텐츠 정보
     * @return 수정된 행 수
     */
    int updateContent(Content content);
    
    /**
     * 콘텐츠 상태만 업데이트
     * @param contentId 콘텐츠 ID
     * @param status 새로운 상태
     * @param lastUpdateId 수정자 ID
     * @return 수정된 행 수
     */
    int updateContentStatus(@Param("contentId") Long contentId, @Param("status") String status, @Param("lastUpdateId") String lastUpdateId);

    /**
     * 콘텐츠 삭제 (논리 삭제)
     * @param contentId 콘텐츠 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteContent(@Param("contentId") Long contentId, @Param("deleteId") String deleteId);
    
    /**
     * 콘텐츠 코드로 중복 체크
     * @param contentCode 콘텐츠 코드
     * @param contentId 제외할 콘텐츠 ID (수정 시 사용)
     * @return 중복된 콘텐츠 개수
     */
    int countDuplicateContentCode(@Param("contentCode") String contentCode, @Param("excludeContentId") Long contentId);
    
    // ========== 통계 및 집계 관련 메서드 ==========
    
    /**
     * 카테고리별 콘텐츠 개수 조회
     * @param categoryId 카테고리 ID
     * @return 콘텐츠 개수
     */
    int countContentByCategoryId(Long categoryId);
    
    /**
     * 접근 권한별 개수 조회
     * @return 접근 권한별 개수 맵
     */
    List<Content> selectContentCountByAccessLevel();

    // ========== API 전용 메서드 ==========

    /**
     * API용 활성화된 콘텐츠 목록 조회 (검색 기능 포함)
     * @param params 검색 파라미터 (page, size, categoryId, searchKeyword, selectedSearchField)
     * @return 콘텐츠 목록
     */
    List<Content> selectActiveContentsForApi(Map<String, Object> params);

    /**
     * API용 활성화된 콘텐츠 총 개수 조회 (검색 조건 적용)
     * @param params 검색 파라미터
     * @return 총 개수
     */
    Long countActiveContentsForApi(Map<String, Object> params);

    // ========== 카테고리 관련 메서드 ==========

    /**
     * 최상위 카테고리 목록 조회 (콘텐츠 관리용)
     * @return 최상위 카테고리 목록
     */
    List<ContentCategory> selectRootCategoriesForContentManagement();

    /**
     * 하위 카테고리 목록 조회
     * @param parentId 부모 카테고리 ID
     * @return 하위 카테고리 목록
     */
    List<ContentCategory> selectChildCategories(Long parentId);
}
