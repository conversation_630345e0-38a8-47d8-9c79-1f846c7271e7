<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>콘텐츠 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="콘텐츠를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/common/admin-list.css" rel="stylesheet">
    <link href="/css/manage/content/content.css" rel="stylesheet">
    <link href="/css/manage/content/list.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="content-page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>콘텐츠 관리</h2>
                        <p>콘텐츠를 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/content/new}" class="btn btn-primary">
                            새 콘텐츠 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 성공 메시지 -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <span th:text="${successMessage}">성공 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 최상위 카테고리 필터 -->
                <div class="category-filter-section">
                    <div class="row">
                        <div class="col-12">
                            <div class="category-filter-container">
                                <h5 class="filter-title">카테고리 선택</h5>
                                <div class="category-buttons">
                                    <button type="button" class="btn btn-outline-primary category-btn active" data-category-id="all">
                                        전체
                                    </button>
                                    <button type="button" 
                                            th:each="category : ${rootCategories}"
                                            th:data-category-id="${category.categoryId}"
                                            th:text="${category.categoryName}"
                                            class="btn btn-outline-primary category-btn">
                                        카테고리명
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 콘텐츠 목록 -->
                <!-- 공통 검색/필터/정렬 컨트롤 -->
                <div th:replace="~{fragments/admin-list-controls :: controls(
                    searchFields='title:콘텐츠명,content_code:콘텐츠코드,category_name:카테고리명',
                    filterOptions='status:상태,access_level:접근권한',
                    sortOptions='content_id:등록순,title:콘텐츠명,content_code:콘텐츠코드,category_name:카테고리명,access_level:접근권한,create_date:등록일,last_update_date:수정일',
                    searchPlaceholder='검색어를 입력해주세요.',
                    showDateRange=false,
                    showNumberRange=false
                )}"></div>

                <!-- 동적 테이블 컨테이너 -->
                <div th:replace="~{fragments/admin-list-controls :: table-container}"></div>

                <!-- 페이징 네비게이션 -->
                <div th:replace="~{fragments/admin-list-controls :: pagination(${listResponse})}"></div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script src="/js/manage/admin-list-manager.js"></script>
        <script th:inline="javascript">
            // 페이지별 설정
            const pageConfig = {
                apiEndpoint: '/manage/content/search',
                customRowRenderer: function(items, response) {
                    if (!items || items.length === 0) {
                        return `
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">등록된 콘텐츠가 없습니다.</p>
                            </div>
                        `;
                    }

                    let html = `
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col" style="width: 60px;">번호</th>
                                        <th scope="col" style="width: 200px;">콘텐츠명</th>
                                        <th scope="col" style="width: 150px;">콘텐츠 코드</th>
                                        <th scope="col" style="width: 200px;">카테고리</th>
                                        <th scope="col" style="width: 100px;">접근 권한</th>
                                        <th scope="col" style="width: 80px;">상태</th>
                                        <th scope="col" style="width: 80px;">문제 연결</th>
                                        <th scope="col" style="width: 120px;">등록일</th>
                                        <th scope="col" style="width: 120px;">관리</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    items.forEach((content, index) => {
                        const rowNumber = (response.currentPage - 1) * response.pageSize + index + 1;
                        const statusClass = content.status === 'ACTIVE' ? 'text-success' : 'text-danger';
                        const statusText = content.status === 'ACTIVE' ? '활성' : '비활성';
                        const accessLevelText = content.accessLevelDisplay || content.accessLevel;
                        const categoryPath = content.categoryPath || '미분류';
                        const quizCount = content.quizMappingCount || 0;
                        const createDate = content.createDate ? new Date(content.createDate).toLocaleDateString('ko-KR') : '-';

                        html += `
                            <tr data-content-id="${content.contentId}">
                                <td class="text-center">${rowNumber}</td>
                                <td>
                                    <div class="content-title-cell">
                                        <strong>${content.title || ''}</strong>
                                        <small class="text-muted d-block">ID: ${content.contentId}</small>
                                    </div>
                                </td>
                                <td>
                                    <code class="content-code">${content.contentCode || ''}</code>
                                </td>
                                <td>
                                    <span class="category-path">${categoryPath}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">${accessLevelText}</span>
                                </td>
                                <td class="text-center">
                                    <button type="button" 
                                            class="btn btn-sm status-toggle-btn ${content.status === 'ACTIVE' ? 'btn-success' : 'btn-secondary'}"
                                            data-content-id="${content.contentId}"
                                            data-current-status="${content.status}">
                                        <i class="fas ${content.status === 'ACTIVE' ? 'fa-check' : 'fa-times'}"></i>
                                        ${statusText}
                                    </button>
                                </td>
                                <td class="text-center">
                                    <span class="badge ${quizCount > 0 ? 'bg-primary' : 'bg-secondary'}">${quizCount}개</span>
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">${createDate}</small>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-primary content-detail-btn"
                                                data-content-id="${content.contentId}"
                                                title="상세보기">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-warning content-edit-btn"
                                                data-content-id="${content.contentId}"
                                                title="수정">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger content-delete-btn"
                                                data-content-id="${content.contentId}"
                                                title="삭제">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                                </tbody>
                            </table>
                        </div>
                    `;

                    return html;
                },
                
                // 카테고리 필터 처리
                categoryFilter: {
                    enabled: true,
                    buttonSelector: '.category-btn',
                    activeClass: 'active',
                    allCategoryValue: 'all'
                },

                // 상태 토글 처리
                statusToggle: {
                    enabled: true,
                    buttonSelector: '.status-toggle-btn',
                    endpoint: '/manage/content/{id}/toggle-status'
                },

                // 상세보기 처리
                detailView: {
                    enabled: true,
                    buttonSelector: '.content-detail-btn',
                    endpoint: '/manage/content/{id}/detail'
                },

                // 수정 처리
                editAction: {
                    enabled: true,
                    buttonSelector: '.content-edit-btn',
                    redirectUrl: '/manage/content/{id}/edit'
                },

                // 삭제 처리
                deleteAction: {
                    enabled: true,
                    buttonSelector: '.content-delete-btn',
                    endpoint: '/manage/content/{id}',
                    confirmMessage: '정말로 이 콘텐츠를 삭제하시겠습니까?'
                }
            };

            // 페이지 초기화
            $(document).ready(function() {
                // 관리자 리스트 매니저 초기화
                window.adminListManager = new AdminListManager(pageConfig);
                
                // 초기 데이터 로드
                window.adminListManager.loadData();
            });
        </script>
    </th:block>
</body>
</html>
