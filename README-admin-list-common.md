# 관리자 리스트 페이지 공통 기능

## 🎯 프로젝트 개요

한림공원 QR 시스템의 관리자 페이지에서 사용하는 리스트 기능을 표준화하고 재사용 가능하게 만든 공통 컴포넌트입니다.

### ✨ 주요 특징

- **🔍 통합 검색**: 여러 필드에 대한 키워드 검색
- **🎛️ 동적 필터링**: 설정 기반 필터 옵션
- **📊 유연한 정렬**: 다양한 정렬 기준 지원
- **📄 스마트 페이징**: 성능 최적화된 페이징
- **⚡ AJAX 기반**: 페이지 새로고침 없는 부드러운 UX
- **📱 반응형 디자인**: 모바일/태블릿 완벽 지원
- **🎨 일관된 UI/UX**: Bootstrap 기반 통일된 디자인
- **♿ 접근성**: WCAG 가이드라인 준수

## 🏗️ 아키텍처

```
Frontend (JavaScript + Thymeleaf)
├── AdminListManager (JavaScript 클래스)
├── admin-list-controls.html (Thymeleaf 프래그먼트)
└── admin-list.css (반응형 CSS)

Backend (Spring Boot + MyBatis)
├── AdminListSearch (검색 조건 Model)
├── AdminListResponse (응답 Model)
├── BaseAdminListService (공통 서비스)
├── AdminListMapper (공통 매퍼 인터페이스)
└── admin-list-mapper.xml (동적 쿼리)

Utilities
├── AdminListUtils (유틸리티 클래스)
└── 설정 기반 커스터마이징
```

## 🚀 빠른 시작

### 1. 의존성 확인

이 공통 기능은 다음 기술 스택을 기반으로 합니다:

- **Spring Boot 3.x**
- **MyBatis 3.x**
- **Thymeleaf 3.x**
- **jQuery 3.x**
- **Bootstrap 5.x**

### 2. 새로운 도메인에 적용

```java
// 1. Mapper 확장
@Mapper
public interface YourDomainMapper extends AdminListMapper<YourDomain> {
    // 기존 CRUD 메서드들...
}

// 2. Service 확장
@Service
public class YourDomainService extends BaseAdminListService<YourDomain> {
    @Override
    protected AdminListMapper<YourDomain> getMapper() {
        return yourDomainMapper;
    }
    
    @Override
    protected String getTableName() {
        return "your_table_name";
    }
}

// 3. Controller 엔드포인트 추가
@PostMapping("/search")
@ResponseBody
public AdminListResponse<YourDomain> search(@RequestBody AdminListSearch searchCondition) {
    return yourDomainService.findListWithConditions(searchCondition);
}
```

### 3. HTML 템플릿 작성

```html
<!-- 공통 검색/필터/정렬 컨트롤 -->
<div th:replace="fragments/admin-list-controls :: controls(
    searchFields='field1:필드1,field2:필드2',
    filterOptions='status:상태:전체,ACTIVE:활성,INACTIVE:비활성',
    sortOptions='id:ID,name:이름,create_date:등록일',
    searchPlaceholder='검색어를 입력하세요'
)"></div>

<!-- 동적 테이블 컨테이너 -->
<div th:replace="fragments/admin-list-controls :: table-container"></div>

<!-- 페이징 네비게이션 -->
<div th:replace="fragments/admin-list-controls :: pagination(${listResponse})"></div>
```

### 4. JavaScript 초기화

```javascript
const listManager = new AdminListManager({
    apiEndpoint: '/manage/your-domain/search',
    customRowRenderer: function(items, response) {
        // 커스텀 테이블 렌더링 로직
        return renderYourDomainTable(items);
    }
});
```

## 📁 파일 구조

```
src/main/
├── java/kr/wayplus/qr_hallimpark/
│   ├── model/
│   │   ├── AdminListSearch.java          # 검색 조건 Model
│   │   └── AdminListResponse.java        # 응답 Model
│   ├── common/
│   │   ├── service/
│   │   │   ├── AdminListService.java     # 공통 서비스 인터페이스
│   │   │   └── impl/BaseAdminListService.java # 공통 서비스 구현체
│   │   ├── mapper/AdminListMapper.java   # 공통 매퍼 인터페이스
│   │   └── utils/AdminListUtils.java     # 유틸리티 클래스
│   └── [domain]/
│       ├── mapper/[Domain]Mapper.java    # 도메인별 매퍼 (확장)
│       ├── service/[Domain]Service.java  # 도메인별 서비스 (확장)
│       └── controller/Manage[Domain]Controller.java # 컨트롤러
├── resources/
│   ├── templates/
│   │   ├── fragments/admin-list-controls.html # 공통 프래그먼트
│   │   └── manage/[domain]/list.html     # 도메인별 리스트 페이지
│   ├── static/
│   │   ├── js/manage/admin-list-manager.js # JavaScript 라이브러리
│   │   └── css/manage/common/admin-list.css # 공통 CSS
│   └── sqlmapper/
│       ├── common/admin-list-mapper.xml  # 공통 동적 쿼리
│       └── DML/[domain]-mapper.xml       # 도메인별 쿼리 (확장)
└── docs/
    ├── admin-list-common-guide.md        # 상세 사용 가이드
    └── README-admin-list-common.md       # 이 파일
```

## 🎨 커스터마이징

### 검색 필드 설정
```
searchFields='field1:라벨1,field2:라벨2,field3:라벨3'
```

### 필터 옵션 설정
```
filterOptions='field1:라벨1:전체,VALUE1:옵션1,VALUE2:옵션2|field2:라벨2:전체,VALUE3:옵션3'
```

### 정렬 옵션 설정
```
sortOptions='field1:라벨1,field2:라벨2,field3:라벨3'
```

### 날짜/숫자 범위 검색
```html
showDateRange=true
showNumberRange=true
```

## 🔧 고급 기능

### 커스텀 렌더러
```javascript
const pageConfig = {
    customRowRenderer: function(items, response) {
        // 완전히 커스터마이징된 테이블 렌더링
        return customTableHtml;
    }
};
```

### 콜백 함수
```javascript
const pageConfig = {
    onDataLoaded: function(response) { /* 데이터 로드 완료 시 */ },
    onError: function(error) { /* 에러 발생 시 */ },
    onSearchStart: function(condition) { /* 검색 시작 시 */ }
};
```

## 📊 성능 최적화

- **사용자 친화적 검색**: 검색 버튼 클릭 또는 엔터키 입력 시 검색 실행
- **효율적 쿼리**: MyBatis 동적 쿼리로 필요한 조건만 적용
- **페이징**: LIMIT/OFFSET을 통한 메모리 효율적 페이징
- **캐싱**: 검색 조건 URL 동기화로 브라우저 캐시 활용

## 🧪 테스트

### 적용 예제
현재 **콘텐츠 카테고리 관리** 페이지에 적용되어 있습니다:

- **기존 페이지**: `/manage/content-category`
- **신규 페이지**: `/manage/content-category/list`
- **검색 엔드포인트**: `/manage/content-category/search`

### 테스트 시나리오
1. ✅ 키워드 검색 (카테고리명, 설명)
2. ✅ 정렬 (ID, 카테고리명, 등록일, 수정일)
3. ✅ 페이징 (10, 20, 50, 100개씩)
4. ✅ 반응형 디자인 (모바일/태블릿)
5. ✅ AJAX 기반 부드러운 UX
6. ✅ 에러 처리 및 로딩 상태

## 📚 문서

- **[상세 사용 가이드](docs/admin-list-common-guide.md)**: 완전한 구현 가이드
- **[API 명세서](docs/admin-list-common-guide.md#api-명세서)**: REST API 문서
- **[마이그레이션 가이드](docs/admin-list-common-guide.md#마이그레이션-가이드)**: 기존 페이지 전환 방법

## 🤝 기여하기

새로운 기능이나 개선사항이 있다면:

1. 기능 요청 또는 버그 리포트 작성
2. 코드 리뷰 및 테스트
3. 문서 업데이트

## 📄 라이선스

이 프로젝트는 한림공원 QR 시스템의 일부로 내부 사용을 위해 개발되었습니다.

---

**개발자**: amaham1  
**개발 기간**: 2024.06  
**버전**: 1.0.0
