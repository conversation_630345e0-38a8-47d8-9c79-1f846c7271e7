/* 한림공원 QR 체험 - 콘텐츠 관리 스타일 */

/* 카테고리 필터 섹션 */
.category-filter-section {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

.filter-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.category-filter-container {
    width: 100%;
}

.category-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.category-btn {
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid #007bff;
    background: white;
    color: #007bff;
}

.category-btn:hover {
    background: #007bff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.category-btn.active {
    background: #007bff;
    color: white;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

/* 콘텐츠 테이블 스타일 */
.content-title-cell {
    max-width: 200px;
}

.content-title-cell strong {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.content-title-cell small {
    font-size: 0.75rem;
    color: #6c757d;
}

.content-code {
    background-color: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    border: 1px solid #dee2e6;
}

.category-path {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 상태 토글 버튼 */
.status-toggle-btn {
    min-width: 70px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.status-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-toggle-btn.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.status-toggle-btn.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* 접근 권한 배지 */
.badge.bg-info {
    background-color: #17a2b8 !important;
    color: white;
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* 문제 연결 배지 */
.badge.bg-primary {
    background-color: #007bff !important;
    color: white;
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* 관리 버튼 그룹 */
.btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 4px;
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

.content-detail-btn {
    color: #007bff;
    border-color: #007bff;
}

.content-detail-btn:hover {
    background-color: #007bff;
    color: white;
}

.content-edit-btn {
    color: #ffc107;
    border-color: #ffc107;
}

.content-edit-btn:hover {
    background-color: #ffc107;
    color: #212529;
}

.content-delete-btn {
    color: #dc3545;
    border-color: #dc3545;
}

.content-delete-btn:hover {
    background-color: #dc3545;
    color: white;
}

/* 테이블 반응형 */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .content-title-cell {
        max-width: 150px;
    }
    
    .category-buttons {
        justify-content: center;
    }
    
    .category-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .btn-group .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }
}

/* 빈 상태 스타일 */
.text-center.py-5 {
    padding: 3rem 1rem !important;
}

.text-center.py-5 i {
    color: #dee2e6;
    margin-bottom: 1rem;
}

.text-center.py-5 p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}

/* 로딩 상태 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 페이지 헤더 */
.content-page-header {
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.content-page-header h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.content-page-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

.content-page-header .btn-primary {
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.content-page-header .btn-primary:hover {
    background-color: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 알림 메시지 스타일 */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert .btn-close {
    padding: 0.5rem;
    margin: -0.5rem -0.5rem -0.5rem auto;
}
