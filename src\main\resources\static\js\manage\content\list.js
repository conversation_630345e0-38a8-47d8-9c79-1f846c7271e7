/**
 * 콘텐츠 관리 리스트 페이지 전용 JavaScript
 * - 콘텐츠 관련 특화 기능 처리
 * - 상태 토글, 상세보기, 수정, 삭제 등
 */

class ContentListManager {
    constructor() {
        this.csrfToken = $('meta[name="_csrf"]').attr('content');
        this.csrfHeader = $('meta[name="_csrf_header"]').attr('content');
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupAjaxDefaults();
    }

    setupAjaxDefaults() {
        // CSRF 토큰 설정
        $.ajaxSetup({
            beforeSend: (xhr) => {
                if (this.csrfHeader && this.csrfToken) {
                    xhr.setRequestHeader(this.csrfHeader, this.csrfToken);
                }
            }
        });
    }

    bindEvents() {
        // 상태 토글 버튼 이벤트
        $(document).on('click', '.status-toggle-btn', (e) => {
            e.preventDefault();
            this.handleStatusToggle($(e.currentTarget));
        });

        // 상세보기 버튼 이벤트
        $(document).on('click', '.content-detail-btn', (e) => {
            e.preventDefault();
            this.handleDetailView($(e.currentTarget));
        });

        // 수정 버튼 이벤트
        $(document).on('click', '.content-edit-btn', (e) => {
            e.preventDefault();
            this.handleEdit($(e.currentTarget));
        });

        // 삭제 버튼 이벤트
        $(document).on('click', '.content-delete-btn', (e) => {
            e.preventDefault();
            this.handleDelete($(e.currentTarget));
        });

        // 카테고리 필터 버튼 이벤트
        $(document).on('click', '.category-btn', (e) => {
            e.preventDefault();
            this.handleCategoryFilter($(e.currentTarget));
        });
    }

    /**
     * 상태 토글 처리
     */
    handleStatusToggle($button) {
        const contentId = $button.data('content-id');
        const currentStatus = $button.data('current-status');
        
        if (!contentId) {
            this.showAlert('콘텐츠 ID가 없습니다.', 'error');
            return;
        }

        // 버튼 비활성화
        $button.prop('disabled', true);
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin"></i> 처리중...');

        $.ajax({
            url: `/manage/content/${contentId}/toggle-status`,
            method: 'POST',
            success: (response) => {
                if (response.success) {
                    // 버튼 상태 업데이트
                    const newStatus = response.newStatus;
                    const isActive = newStatus === 'ACTIVE';
                    
                    $button.removeClass('btn-success btn-secondary')
                           .addClass(isActive ? 'btn-success' : 'btn-secondary')
                           .data('current-status', newStatus);
                    
                    $button.html(`<i class="fas ${isActive ? 'fa-check' : 'fa-times'}"></i> ${response.statusDisplay}`);
                    
                    this.showAlert(response.message, 'success');
                } else {
                    this.showAlert(response.message || '상태 변경에 실패했습니다.', 'error');
                    $button.html(originalHtml);
                }
            },
            error: (xhr) => {
                console.error('Status toggle error:', xhr);
                this.showAlert('상태 변경 중 오류가 발생했습니다.', 'error');
                $button.html(originalHtml);
            },
            complete: () => {
                $button.prop('disabled', false);
            }
        });
    }

    /**
     * 상세보기 처리
     */
    handleDetailView($button) {
        const contentId = $button.data('content-id');
        
        if (!contentId) {
            this.showAlert('콘텐츠 ID가 없습니다.', 'error');
            return;
        }

        $.ajax({
            url: `/manage/content/${contentId}/detail`,
            method: 'GET',
            success: (response) => {
                if (response.success) {
                    this.showContentDetailModal(response.data);
                } else {
                    this.showAlert(response.message || '콘텐츠 정보를 불러올 수 없습니다.', 'error');
                }
            },
            error: (xhr) => {
                console.error('Detail view error:', xhr);
                this.showAlert('콘텐츠 정보 조회 중 오류가 발생했습니다.', 'error');
            }
        });
    }

    /**
     * 수정 처리
     */
    handleEdit($button) {
        const contentId = $button.data('content-id');
        
        if (!contentId) {
            this.showAlert('콘텐츠 ID가 없습니다.', 'error');
            return;
        }

        // 수정 페이지로 이동
        window.location.href = `/manage/content/${contentId}/edit`;
    }

    /**
     * 삭제 처리
     */
    handleDelete($button) {
        const contentId = $button.data('content-id');
        
        if (!contentId) {
            this.showAlert('콘텐츠 ID가 없습니다.', 'error');
            return;
        }

        // 삭제 확인
        if (!confirm('정말로 이 콘텐츠를 삭제하시겠습니까?\n삭제된 콘텐츠는 복구할 수 없습니다.')) {
            return;
        }

        // 버튼 비활성화
        $button.prop('disabled', true);
        const originalHtml = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin"></i>');

        $.ajax({
            url: `/manage/content/${contentId}`,
            method: 'DELETE',
            success: (response) => {
                if (response.success) {
                    this.showAlert(response.message, 'success');
                    // 리스트 새로고침
                    if (window.adminListManager) {
                        window.adminListManager.loadData();
                    } else {
                        location.reload();
                    }
                } else {
                    this.showAlert(response.message || '삭제에 실패했습니다.', 'error');
                    $button.html(originalHtml);
                }
            },
            error: (xhr) => {
                console.error('Delete error:', xhr);
                this.showAlert('삭제 중 오류가 발생했습니다.', 'error');
                $button.html(originalHtml);
            },
            complete: () => {
                $button.prop('disabled', false);
            }
        });
    }

    /**
     * 카테고리 필터 처리
     */
    handleCategoryFilter($button) {
        const categoryId = $button.data('category-id');
        
        // 모든 카테고리 버튼에서 active 클래스 제거
        $('.category-btn').removeClass('active');
        
        // 클릭된 버튼에 active 클래스 추가
        $button.addClass('active');
        
        // AdminListManager를 통해 필터 적용
        if (window.adminListManager) {
            if (categoryId === 'all') {
                window.adminListManager.updateFilter('categoryId', null);
            } else {
                window.adminListManager.updateFilter('categoryId', categoryId);
            }
            window.adminListManager.loadData();
        }
    }

    /**
     * 콘텐츠 상세 정보 모달 표시
     */
    showContentDetailModal(content) {
        const modalHtml = `
            <div class="modal fade" id="contentDetailModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">콘텐츠 상세 정보</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">콘텐츠 ID</label>
                                        <p class="form-control-plaintext">${content.contentId}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">콘텐츠 코드</label>
                                        <p class="form-control-plaintext"><code>${content.contentCode || ''}</code></p>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">콘텐츠명</label>
                                <p class="form-control-plaintext">${content.title || ''}</p>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">카테고리</label>
                                        <p class="form-control-plaintext">${content.categoryPath || '미분류'}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">접근 권한</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-info">${content.accessLevelDisplay || content.accessLevel}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">상태</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge ${content.status === 'ACTIVE' ? 'bg-success' : 'bg-secondary'}">
                                                ${content.status === 'ACTIVE' ? '활성' : '비활성'}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">연결된 문제</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge ${(content.quizMappingCount || 0) > 0 ? 'bg-primary' : 'bg-secondary'}">
                                                ${content.quizMappingCount || 0}개
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">생성일</label>
                                        <p class="form-control-plaintext">${content.createDate ? new Date(content.createDate).toLocaleString('ko-KR') : '-'}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">수정일</label>
                                        <p class="form-control-plaintext">${content.lastUpdateDate ? new Date(content.lastUpdateDate).toLocaleString('ko-KR') : '-'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">닫기</button>
                            <button type="button" class="btn btn-primary" onclick="window.location.href='/manage/content/${content.contentId}/edit'">수정</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 기존 모달 제거
        $('#contentDetailModal').remove();
        
        // 새 모달 추가 및 표시
        $('body').append(modalHtml);
        $('#contentDetailModal').modal('show');
        
        // 모달이 닫힐 때 DOM에서 제거
        $('#contentDetailModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    /**
     * 알림 메시지 표시
     */
    showAlert(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // 기존 알림 제거
        $('.alert').remove();
        
        // 새 알림 추가
        $('.container').first().prepend(alertHtml);
        
        // 3초 후 자동 제거
        setTimeout(() => {
            $('.alert').fadeOut(() => {
                $('.alert').remove();
            });
        }, 3000);
    }
}

// 전역 인스턴스 생성
window.contentListManager = new ContentListManager();
